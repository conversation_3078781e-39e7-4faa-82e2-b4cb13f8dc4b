<template>
  <n-modal v-model:show="modelVisible" @update:show="updateVisible" :title="title" preset="card"
    style="width: 100%; height: 100%;" :mask-closable="false" transform-origin="center">
    <n-form ref="formRef" :model="form" :rules="rules" label-placement="top" require-mark-placement="right-hanging"
      class="order-form">

      <!-- 客户信息部分 -->
      <customer-info-section
        :form="form"
        @show-customer-selector="showCustomerSelector"
      />

      <!-- 产品信息部分 -->
      <product-info-section
        :form="form"
        :selected-outbound-org="selectedOutboundOrg"
        @show-vehicle-selector="showVehicleSelector"
        @handle-outbound-org-change="handleOutboundOrgChange"
        @handle-sale-price-change="handleSalePriceChange"
        @handle-discount-change="handleDiscountChange"
        @handle-sales-expense-change="handleSalesExpenseChange"
      />

      <!-- 付款方式部分 -->
      <payment-method-section
        :form="form"
        :loan-channel-options="loanChannelOptions"
        :loan-months-options="loanMonthsOptions"
        @handle-loan-amount-change="handleLoanAmountChange"
        @handle-loan-initial-amount-change="handleLoanInitialAmountChange"
        @handle-loan-fee-change="handleLoanFeeChange"
      />

      <!-- 车辆置换部分 -->
      <vehicle-exchange-section :form="form" />

      <!-- 车辆保险部分 -->
      <insurance-section :form="form" />

      <!-- 售前衍生收入部分 -->
      <derivative-income-section :form="form" />

      <!-- 赠品明细部分 -->
      <gift-items-section :form="form"  ref="giftItemsSectionRef" />

      <!-- 订单备注部分 -->
      <remark-section :form="form" />

      <!-- 财务结算部分 -->
      <financial-settlement-section
        :form="form"
        @handle-sale-price-change="handleSalePriceChange"
        @handle-discount-change="handleDiscountChange"
        @handle-exclusive-discount-change="handleExclusiveDiscountChange"
      />
    </n-form>
    <template #footer>
      <n-space justify="end">
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" @click="handleSave">确定</n-button>
      </n-space>
    </template>

    <!-- 客户选择器 -->
    <customer-selector v-model:visible="customerSelectorVisible" @select="handleCustomerSelected" />

    <!-- 车辆选择器 -->
    <vehicle-s-k-u-selector v-model:visible="vehicleSelectorVisible" @select="handleVehicleSelected" />
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { NModal, NForm, NButton, NSpace } from 'naive-ui'
import vehicleOrderApi from '@/api/vehicleOrder'
import messages from '@/utils/messages'
import { convertNumberToChinese } from '@/utils/money'
import { dictOptions } from '@/mock/dictData'
import { createOrderFormRules } from '@/utils/validators/orderFormValidators'

// 引入选择器组件
import CustomerSelector from '@/components/customer/CustomerSelector.vue'
import VehicleSKUSelector from '@/components/inventory/VehicleSKUSelector.vue'

// 引入各个部分组件
import CustomerInfoSection from '@/components/orders/sections/CustomerInfoSection.vue'
import ProductInfoSection from '@/components/orders/sections/ProductInfoSection.vue'
import PaymentMethodSection from '@/components/orders/sections/PaymentMethodSection.vue'
import VehicleExchangeSection from '@/components/orders/sections/VehicleExchangeSection.vue'
import InsuranceSection from '@/components/orders/sections/InsuranceSection.vue'
import DerivativeIncomeSection from '@/components/orders/sections/DerivativeIncomeSection.vue'
import GiftItemsSection from '@/components/orders/sections/GiftItemsSection.vue'
import RemarkSection from '@/components/orders/sections/RemarkSection.vue'
import FinancialSettlementSection from '@/components/orders/sections/FinancialSettlementSection.vue'

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '新增订单'
  },
  initialData: {
    type: Object,
    default: () => ({})
  },
  vehicleCategoryOptions: {
    type: Array,
    default: () => []
  },
  orderStatusOptions: {
    type: Array,
    default: () => []
  }
})

// 定义组件事件
const emit = defineEmits(['update:visible', 'save', 'cancel'])

// 组件状态
const formRef = ref(null)
const customerSelectorVisible = ref(false)
const vehicleSelectorVisible = ref(false)
const giftItemsSectionRef = ref(null)

// 出库单位选择器相关
const selectedOutboundOrg = ref(null)

// 贷款渠道选项 - 从字典数据中获取
const loanChannelOptions = computed(() => {
  return dictOptions['loan_channel'].map(item => ({
    label: item.option_label,
    value: item.option_value
  }))
})

// 贷款期限选项 - 从字典数据中获取
const loanMonthsOptions = computed(() => {
  return dictOptions['loan_months'].map(item => ({
    label: item.option_label,
    value: item.option_value
  }))
})

// 计算属性：模态框可见性
const modelVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 更新可见性
const updateVisible = (val) => {
  emit('update:visible', val)
}

// 处理日期转换
const convertDateToTimestamp = (date) => {
  if (!date) return null
  if (typeof date === 'number') return date
  if (date instanceof Date) return date.getTime()
  return null
}

// 表单数据
const form = reactive({
  id: null,
  dealDate: Date.now(), // 默认为当前日期
  invoiceOrgId: null, // 销售单位ID
  invoiceOrgName: '',

  // 出库信息
  outboundOrgId: null, // 出库单位ID
  outboundOrgName: '', // 出库单位名称
  expectedOutboundDate: Date.now(), // 默认为当前日期

  // 客户信息
  customerId: null,
  customerName: '',
  customerPhone: '',
  customerAddress: '',
  salesperson: '',
  salesAgentId: null, // 销售顾问ID
  salesLeaderId: null, // 销售主管ID

  // 车辆信息
  skuId: null, // 车辆SKU ID
  vehicleBrand: '', // 车辆品牌
  vehicleSeries: '', // 车型
  vehicleConfig: '', // 配置
  vehicleColorCode: '', // 颜色代码
  vehicleSbPrice: 0, // 启票价格
  vehicleSalePrice: 0, // 销售价格

  // 金额信息
  depositAmount: 0, // 已付定金
  depositDeductible: true, // 定金是否转车款，默认为是
  discountAmount: 0, // 优惠金额
  exclusiveDiscountType: 'NONE', // 专享优惠类型，默认为"无"
  exclusiveDiscountAmount: 0, // 专享优惠金额
  salesExpense: 0, // 销售费用
  finalPrice: 0, // 成交总价
  finalPriceChinese: '零', // 成交总价（大写）
  profitRate: 0, // 预估毛利率
  profitAmount: 0, // 预估毛利润

  // 付款方式
  paymentMethod: 'FULL', // 默认全款，可选值：FULL（全款）、LOAN（贷款）

  // 贷款信息 - 付款方式为贷款时使用
  loanChannel: '', // 贷款渠道
  loanAmount: 0, // 贷款金额
  loanInitialAmount: 0, // 首付金额
  loanInitialRatio: 0, // 首付比例
  loanMonths: null, // 贷款期限（月）
  loanRebateAmount: 0, // 分期返利（客户）
  loanRebateDeductible: true, // 分期返利是否转车款，默认为是
  loanFee: 0, // 分期服务费，默认为0

  // 二手车置换信息
  hasUsedVehicle: 'NO', // 是否有车辆置换，默认为"无"，可选值：YES（有）、NO（无）
  usedVehicleId: '', // 二手车车牌号
  usedVehicleVin: '', // 二手车VIN
  usedVehicleAmount: 0, // 二手车置换金额
  usedVehicleDeductible: true, // 是否转车款，默认为是
  usedVehicleDiscountAmount: 0, // 二手车置换补贴
  usedVehicleDiscountDeductible: true, // 置换补贴是否转车款，默认为是
  usedVehicleBound: '', // 二手车品牌
  usedVehicleModel: '', // 二手车车型
  usedVehicleColor: '', // 二手车颜色

  // 车辆保险信息
  hasInsurance: 'NO', // 是否购买保险，默认为"无"，可选值：YES（有）、NO（无）

  // 其他衍生收入信息
  hasDerivativeIncome: 'NO', // 是否产生其他衍生收入，默认为"无"，可选值：YES（有）、NO（无）
  notaryFee: 0, // 公证费
  carefreeIncome: 0, // 畅行无忧收入
  extendedWarrantyIncome: 0, // 延保收入
  vpsIncome: 0, // VPS收入
  preInterest: 0, // 前置利息
  licensePlateFee: 0, // 挂牌费
  tempPlateFee: 0, // 临牌费
  deliveryEquipment: 0, // 外卖装具

  // 赠品明细信息
  hasGiftItems: 'NO', // 是否有赠品，默认为"无"，可选值：YES（有）、NO（无）
  giftItems: [] // 赠品明细数组
})

// 初始化表单数据
if (props.initialData) {
  const data = { ...props.initialData }

  // 确保日期是时间戳格式
  if (data.dealDate) {
    data.dealDate = convertDateToTimestamp(data.dealDate)
  }

  Object.assign(form, data)
}

// 表单验证规则 - 使用从工具函数导入的校验规则
const rules = computed(() => createOrderFormRules(form))

// 显示客户选择器
const showCustomerSelector = () => {
  customerSelectorVisible.value = true
}

// 显示车辆选择器
const showVehicleSelector = () => {
  vehicleSelectorVisible.value = true
}

// 处理车辆选择
const handleVehicleSelected = (vehicle) => {
  if (!vehicle) return

  // 更新车辆信息
  form.skuId = vehicle.id
  form.vehicleBrand = vehicle.brand || ''
  form.vehicleSeries = vehicle.series || ''
  form.vehicleConfig = vehicle.configName || ''
  form.vehicleColorCode = vehicle.colorCode || ''
  form.vehicleSbPrice = vehicle.sbPrice || 0

  // 默认销售价为启票价的1.35倍
  form.vehicleSalePrice = parseFloat((form.vehicleSbPrice * 1.35).toFixed(2))

  // 更新金额计算
  calculateFinalPrice()
  calculateProfitRate()
}

// 处理销售价格变化
const handleSalePriceChange = () => {
  calculateFinalPrice()
  calculateProfitRate()
}

// 计算成交总价
const calculateFinalPrice = () => {
  // 基础成交价 = 销售价格 - 优惠金额
  let basePrice = Math.max(0, form.vehicleSalePrice - (form.discountAmount || 0))

  // 如果有专享优惠且类型不是"无"，减去专享优惠金额
  if (form.exclusiveDiscountType && form.exclusiveDiscountType !== 'NONE') {
    basePrice = Math.max(0, basePrice - (form.exclusiveDiscountAmount || 0))
  }

  // 如果有衍生收入，加上衍生收入总金额
  if (form.hasDerivativeIncome === 'YES') {
    basePrice += (
      (form.notaryFee || 0) +
      (form.carefreeIncome || 0) +
      (form.extendedWarrantyIncome || 0) +
      (form.vpsIncome || 0) +
      (form.preInterest || 0) +
      (form.licensePlateFee || 0) +
      (form.tempPlateFee || 0) +
      (form.deliveryEquipment || 0) +
      (form.otherIncome || 0)
    )
  }

  // 如果是分期付款，加上分期服务费
  if (form.paymentMethod === 'LOAN') {
    basePrice += (form.loanFee || 0)
  }

  form.finalPrice = basePrice
  // 更新大写金额
  form.finalPriceChinese = convertNumberToChinese(form.finalPrice)
}

// 计算预估毛利率和毛利润
const calculateProfitRate = () => {
  // 计算总成本 = 启票价格 + 销售费用
  const totalCost = form.vehicleSbPrice + (form.salesExpense || 0)

  // 计算预估毛利润 = 成交总价 - 总成本
  form.profitAmount = form.finalPrice - totalCost

  if (totalCost > 0) {
    // 毛利率 = 毛利润 / 总成本 * 100%
    let rate = (form.profitAmount / totalCost) * 100
    // 如果毛利率为负，则显示为0
    rate = rate < 0 ? 0 : rate
    // 保留2位小数
    form.profitRate = parseFloat(rate.toFixed(2))
  } else {
    form.profitRate = 0
  }
}

// 监听车辆信息变化，自动计算金额
watch([() => form.vehicleSbPrice, () => form.vehicleSalePrice], () => {
  calculateFinalPrice()
  calculateProfitRate()
})

// 监听专享优惠变化，自动计算金额
watch([() => form.exclusiveDiscountType, () => form.exclusiveDiscountAmount], () => {
  calculateFinalPrice()
  calculateProfitRate()
})

// 监听衍生收入变化，自动计算金额
watch([
  () => form.hasDerivativeIncome,
  () => form.notaryFee,
  () => form.carefreeIncome,
  () => form.extendedWarrantyIncome,
  () => form.vpsIncome,
  () => form.preInterest,
  () => form.licensePlateFee,
  () => form.tempPlateFee,
  () => form.deliveryEquipment,
  () => form.otherIncome
], () => {
  calculateFinalPrice()
  calculateProfitRate()
})

// 监听成交总价变化，更新贷款相关金额
watch(() => form.finalPrice, (newValue, oldValue) => {
  if (newValue > 0 && form.paymentMethod === 'LOAN') {
    // 如果成交总价变化，且付款方式为贷款
    if (oldValue > 0) {
      // 如果之前有值，保持首付比例不变，重新计算首付金额和贷款金额
      const ratio = form.loanInitialRatio / 100
      // 首付金额 = 成交总价 * 首付比例
      form.loanInitialAmount = parseFloat((newValue * ratio).toFixed(2))
      // 贷款金额 = 成交总价 - 首付金额
      form.loanAmount = parseFloat((newValue - form.loanInitialAmount).toFixed(2))
    } else {
      // 如果之前没有值，设置默认值：首付30%，贷款70%
      form.loanInitialRatio = 30
      form.loanInitialAmount = parseFloat((newValue * 0.3).toFixed(2))
      form.loanAmount = parseFloat((newValue * 0.7).toFixed(2))
    }
  }
})

// 处理优惠金额变化
const handleDiscountChange = () => {
  calculateFinalPrice()
  calculateProfitRate()
}

// 处理销售费用变化
const handleSalesExpenseChange = () => {
  calculateProfitRate()
}

// 处理专享优惠变化
const handleExclusiveDiscountChange = () => {
  calculateFinalPrice()
  calculateProfitRate()
}

// 处理贷款金额变化
const handleLoanAmountChange = () => {
  if (form.finalPrice > 0) {
    // 当贷款金额变化时，自动计算首付金额 = 成交总价 - 贷款金额
    form.loanInitialAmount = parseFloat((form.finalPrice - form.loanAmount).toFixed(2))
    // 确保首付金额不小于0
    if (form.loanInitialAmount < 0) {
      form.loanInitialAmount = 0
      // 如果首付金额被调整为0，则贷款金额等于成交总价
      form.loanAmount = form.finalPrice
    }
    // 计算首付比例 = 首付金额 / 成交总价 * 100%
    form.loanInitialRatio = parseFloat(((form.loanInitialAmount / form.finalPrice) * 100).toFixed(2))
  }
}

// 处理首付金额变化
const handleLoanInitialAmountChange = () => {
  if (form.finalPrice > 0) {
    // 当首付金额变化时，自动计算贷款金额 = 成交总价 - 首付金额
    form.loanAmount = parseFloat((form.finalPrice - form.loanInitialAmount).toFixed(2))
    // 确保贷款金额不小于0
    if (form.loanAmount < 0) {
      form.loanAmount = 0
      // 如果贷款金额被调整为0，则首付金额等于成交总价
      form.loanInitialAmount = form.finalPrice
    }
    // 计算首付比例 = 首付金额 / 成交总价 * 100%
    form.loanInitialRatio = parseFloat(((form.loanInitialAmount / form.finalPrice) * 100).toFixed(2))
  }
}

// 处理分期服务费变化
const handleLoanFeeChange = () => {
  // 当分期服务费变化时，重新计算成交价格
  calculateFinalPrice()
  calculateProfitRate()

  // 由于成交价格变化，需要重新计算贷款金额和首付金额
  if (form.finalPrice > 0) {
    // 保持首付比例不变，重新计算首付金额和贷款金额
    const ratio = form.loanInitialRatio / 100
    // 首付金额 = 成交总价 * 首付比例
    form.loanInitialAmount = parseFloat((form.finalPrice * ratio).toFixed(2))
    // 贷款金额 = 成交总价 - 首付金额
    form.loanAmount = parseFloat((form.finalPrice - form.loanInitialAmount).toFixed(2))
  }
}

// 处理出库单位选择
const handleOutboundOrgChange = (org) => {
  if (org) {
    form.outboundOrgId = org.id
    form.outboundOrgName = org.name
    selectedOutboundOrg.value = org
  } else {
    form.outboundOrgId = null
    form.outboundOrgName = ''
    selectedOutboundOrg.value = null
  }
}

// 处理客户选择
const handleCustomerSelected = (customer) => {
  // 更新客户信息
  form.customerId = customer.id
  form.customerName = customer.customerName
  form.customerPhone = customer.mobile
  form.customerAddress = customer.address
  form.salesperson = customer.ownerSellerName

  // 设置销售顾问和销售主管ID
  form.salesAgentId = customer.ownerSellerId || customer.id
  form.salesLeaderId = customer.ownerLeaderId || customer.id

  // 如果是新增，也更新销售单位
  if (!props.isEdit) {
    form.invoiceOrgId = customer.ownerOrgId || null
    form.invoiceOrgName = customer.ownerOrgName || ''
  }
}

// 监听付款方式变化
watch(() => form.paymentMethod, (newValue, oldValue) => {
  // 如果从分期切换到全款，需要重新计算成交价格（去掉分期服务费的影响）
  if (oldValue === 'LOAN' && newValue === 'FULL' && form.loanFee > 0) {
    // 清空分期服务费
    form.loanFee = 0
    // 重新计算成交价格
    calculateFinalPrice()
    calculateProfitRate()
  }

  if (newValue === 'LOAN' && form.finalPrice > 0) {
    // 如果切换到贷款方式，且成交总价大于0，设置默认的贷款金额和首付金额
    if (form.loanInitialAmount === 0 && form.loanAmount === 0) {
      // 如果贷款金额和首付金额都为0，设置默认值：首付30%，贷款70%
      form.loanInitialRatio = 30
      form.loanInitialAmount = parseFloat((form.finalPrice * 0.3).toFixed(2))
      form.loanAmount = parseFloat((form.finalPrice * 0.7).toFixed(2))
    }

    // 重置分期返利相关字段
    form.loanRebateAmount = 0
    form.loanRebateDeductible = true

    // 如果有分期服务费，重新计算成交价格（加上分期服务费的影响）
    if (form.loanFee > 0) {
      calculateFinalPrice()
      calculateProfitRate()

      // 由于成交价格变化，需要重新计算贷款金额和首付金额
      const ratio = form.loanInitialRatio / 100
      form.loanInitialAmount = parseFloat((form.finalPrice * ratio).toFixed(2))
      form.loanAmount = parseFloat((form.finalPrice - form.loanInitialAmount).toFixed(2))
    }
  }

  // 当切换到贷款方式时，始终确保loanMonths有值，避免验证错误
  if (newValue === 'LOAN') {
    // 如果没有选择分期期数，默认设置为12期
    if (form.loanMonths === null || form.loanMonths === undefined || form.loanMonths === '') {
      form.loanMonths = 12 // 默认设置为12期
    }
  }
})

// 监听车辆置换选项变化
watch(() => form.hasUsedVehicle, (newValue) => {
  if (newValue === 'NO') {
    // 如果切换到"无"，清空二手车信息
    form.usedVehicleId = ''
    form.usedVehicleVin = ''
    form.usedVehicleAmount = 0
    form.usedVehicleDeductible = true // 重置为默认值
    form.usedVehicleDiscountAmount = 0
    form.usedVehicleBound = ''
    form.usedVehicleModel = ''
    form.usedVehicleColor = ''
  }
})

// 监听其他衍生收入选项变化
watch(() => form.hasDerivativeIncome, (newValue) => {
  if (newValue === 'NO') {
    // 如果切换到"无"，清空所有衍生收入字段
    form.notaryFee = 0
    form.carefreeIncome = 0
    form.extendedWarrantyIncome = 0
    form.vpsIncome = 0
    form.preInterest = 0
    form.licensePlateFee = 0
    form.tempPlateFee = 0
    form.deliveryEquipment = 0
  }
})

// 监听赠品明细选项变化
watch(() => form.hasGiftItems, (newValue) => {
  if (newValue === 'NO') {
    // 如果切换到"无"，清空赠品明细数组
    form.giftItems = []
    // 如果表格引用存在，调用清空方法
    if (giftItemsSectionRef.value?.giftItemsTableRef) {
      giftItemsSectionRef.value.giftItemsTableRef.clearRows()
    }
  } else if (newValue === 'YES' && form.giftItems.length === 0) {
    // 如果切换到"有"且赠品数组为空，添加一个空行
    if (giftItemsSectionRef.value?.giftItemsTableRef) {
      giftItemsSectionRef.value.giftItemsTableRef.addRow()
    }
  }
})

// 处理保存
const handleSave = () => {
  // 如果是分期付款方式，确保分期期数有值
  if (form.paymentMethod === 'LOAN' && (form.loanMonths === null || form.loanMonths === undefined || form.loanMonths === '')) {
    form.loanMonths = 12 // 默认设置为12期
  }

  // 执行表单验证
  formRef.value?.validate(async (errors, _) => {
    if (errors) {
      console.error('表单验证错误:', errors)
      return messages.error('请完善表单信息')
    }

    try {
      // 构建保存数据
      const data = { ...form }

      // 确保日期是时间戳格式
      if (data.dealDate) {
        data.dealDate = convertDateToTimestamp(data.dealDate)
      }

      // 确保预计出库日期是时间戳格式
      if (data.expectedOutboundDate) {
        data.expectedOutboundDate = convertDateToTimestamp(data.expectedOutboundDate)
      }

      // 添加车辆数据
      data.vehicles = [{
        brand: form.vehicleBrand,
        series: form.vehicleSeries,
        configName: form.vehicleConfig,
        colorCode: form.vehicleColorCode,
        sbPrice: form.vehicleSbPrice,
        quantity: 1,
        salePrice: form.vehicleSalePrice,
        skuId: form.skuId
      }]

      // 构建符合新API的请求数据（使用驼峰命名法）
      const orderPayload = {
        // 客户信息
        customerId: data.customerId,
        salesOrgId: data.invoiceOrgId || data.outboundOrgId, // 销售单位ID
        salesAgentId: data.salesAgentId || data.customerId, // 销售顾问ID，如果没有则使用客户ID
        salesLeaderId: data.salesLeaderId || data.customerId, // 销售主管ID，如果没有则使用客户ID

        // SKU信息
        skuId: form.skuId,

        // 订单日期
        dealDate: data.dealDate, // 订单日期

        // 交付信息
        deliveryDate: data.expectedOutboundDate, // 交付日期
        deliveryOrgId: data.outboundOrgId, // 交付单位ID

        // 金额信息（转换为分）
        sbAmount: Math.round(form.vehicleSbPrice * 100), // 启票价格（分）
        salesAmount: Math.round(form.vehicleSalePrice * 100), // 销售价格（分）
        depositAmount: Math.round(data.depositAmount * 100), // 已付定金（分）
        depositDeductible: data.depositDeductible, // 定金是否转车款
        discountAmount: Math.round(data.discountAmount * 100), // 优惠金额（分）
        exclusiveDiscountType: data.exclusiveDiscountType || 'NONE', // 专享优惠类型
        exclusiveDiscountAmount: data.exclusiveDiscountAmount ? Math.round(data.exclusiveDiscountAmount * 100) : 0, // 专享优惠金额（分）
        salesCostAmount: Math.round(data.salesExpense * 100), // 销售费用（分）
        dealAmount: Math.round(data.finalPrice * 100), // 成交总价（分）
        dealAmountCn: data.finalPriceChinese, // 成交总价大写
        grossProfitAmount: Math.round(data.profitAmount * 100), // 毛利润（分）
        grossProfitRate: data.profitRate, // 毛利率

        // 付款方式
        paymentMethod: data.paymentMethod || 'FULL', // 默认全款

        // 备注
        remark: data.paymentRemark
      }

      // 如果选择了"有"车辆置换且填写了车牌号，添加二手车信息到请求数据
      if (data.hasUsedVehicle === 'YES' && data.usedVehicleId) {
        // 检查置换金额是否大于0
        if (!data.usedVehicleAmount || data.usedVehicleAmount <= 0) {
          return messages.error('二手车置换金额必须大于零')
        }

        orderPayload.usedVehicleId = data.usedVehicleId
        orderPayload.usedVehicleVin = data.usedVehicleVin
        orderPayload.usedVehicleAmount = Math.round(data.usedVehicleAmount * 100)
        orderPayload.usedVehicleDeductible = data.usedVehicleDeductible
        orderPayload.usedVehicleDiscountAmount = data.usedVehicleDiscountAmount ? Math.round(data.usedVehicleDiscountAmount * 100) : 0
        orderPayload.usedVehicleDiscountDeductible = data.usedVehicleDiscountDeductible
        orderPayload.usedVehicleBound = data.usedVehicleBound
        orderPayload.usedVehicleModel = data.usedVehicleModel
        orderPayload.usedVehicleColor = data.usedVehicleColor
      }

      // 如果是贷款方式，添加贷款信息
      if (orderPayload.paymentMethod === 'LOAN') {
        orderPayload.loanChannel = data.loanChannel
        orderPayload.loanAmount = data.loanAmount ? Math.round(data.loanAmount * 100) : 0
        orderPayload.loanInitialAmount = data.loanInitialAmount ? Math.round(data.loanInitialAmount * 100) : 0
        orderPayload.loanMonths = data.loanMonths
        orderPayload.loanRebateAmount = data.loanRebateAmount ? Math.round(data.loanRebateAmount * 100) : 0
        orderPayload.loanRebateDeductible = data.loanRebateDeductible
        orderPayload.loanFee = data.loanFee ? Math.round(data.loanFee * 100) : 0
      }

      // 添加保险选项信息
      orderPayload.hasInsurance = data.hasInsurance

      // 添加其他衍生收入信息
      orderPayload.hasDerivativeIncome = data.hasDerivativeIncome
      if (data.hasDerivativeIncome === 'YES') {
        // 将金额从元转换为分
        orderPayload.notaryFee = Math.round(data.notaryFee * 100)
        orderPayload.carefreeIncome = Math.round(data.carefreeIncome * 100)
        orderPayload.extendedWarrantyIncome = Math.round(data.extendedWarrantyIncome * 100)
        orderPayload.vpsIncome = Math.round(data.vpsIncome * 100)
        orderPayload.preInterest = Math.round(data.preInterest * 100)
        orderPayload.licensePlateFee = Math.round(data.licensePlateFee * 100)
        orderPayload.tempPlateFee = Math.round(data.tempPlateFee * 100)
        orderPayload.deliveryEquipment = Math.round(data.deliveryEquipment * 100)
      }

      // 添加赠品明细信息
      orderPayload.hasGiftItems = data.hasGiftItems
      if (data.hasGiftItems === 'YES' && Array.isArray(data.giftItems) && data.giftItems.length > 0) {
        // 处理赠品明细数据，将单价从元转换为分
        orderPayload.giftItems = data.giftItems.map(item => ({
          ...item,
          unitPrice: Math.round((item.unitPrice || 0) * 100) // 将单价从元转换为分
        }))
      }

      // 调用保存API
      const response = props.isEdit
        ? await vehicleOrderApi.updateOrder(data.id, orderPayload)
        : await vehicleOrderApi.createOrder(orderPayload)

      if (response.code === 200) {
        messages.success(props.isEdit ? '订单更新成功' : '订单创建成功')
        emit('save', response.data)
        modelVisible.value = false
      } else {
        messages.error(response.message || (props.isEdit ? '订单更新失败' : '订单创建失败'))
      }
    } catch (error) {
      messages.error(props.isEdit ? '更新订单失败，请稍后重试' : '创建订单失败，请稍后重试')
    }
  })
}

// 处理取消
const handleCancel = () => {
  modelVisible.value = false
  emit('cancel')
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    id: null,
    dealDate: Date.now(),

    // 重置出库信息
    outboundOrgId: null,
    outboundOrgName: '',
    expectedOutboundDate: Date.now(), // 默认为当前日期

    // 清空客户信息
    customerId: null,
    customerName: '',
    customerPhone: '',
    customerAddress: '',
    salesperson: '',
    salesAgentId: null,
    salesLeaderId: null,

    // 重置车辆信息
    skuId: null,
    vehicleBrand: '',
    vehicleSeries: '',
    vehicleConfig: '',
    vehicleColorCode: '',
    vehicleSbPrice: 0,
    vehicleSalePrice: 0,

    // 重置金额信息
    depositAmount: 0,
    depositDeductible: true,
    discountAmount: 0,
    exclusiveDiscountType: 'NONE',
    exclusiveDiscountAmount: 0,
    salesExpense: 0,
    finalPrice: 0,
    finalPriceChinese: '零',
    profitRate: 0,
    profitAmount: 0,

    // 重置付款方式
    paymentMethod: 'FULL',

    // 重置贷款信息
    loanChannel: '',
    loanAmount: 0,
    loanInitialAmount: 0,
    loanInitialRatio: 0,
    loanMonths: null,
    loanRebateAmount: 0,
    loanRebateDeductible: true,
    loanFee: 0, // 重置分期服务费

    // 重置二手车置换信息
    hasUsedVehicle: 'NO', // 默认为"无"
    usedVehicleId: '',
    usedVehicleVin: '',
    usedVehicleAmount: 0,
    usedVehicleDeductible: true, // 默认为是
    usedVehicleDiscountAmount: 0,
    usedVehicleBound: '',
    usedVehicleModel: '',
    usedVehicleColor: '',

    // 重置保险信息
    hasInsurance: 'NO', // 默认为"无"

    // 重置其他衍生收入信息
    hasDerivativeIncome: 'NO', // 默认为"无"
    notaryFee: 0,
    carefreeIncome: 0,
    extendedWarrantyIncome: 0,
    vpsIncome: 0,
    preInterest: 0,
    licensePlateFee: 0,
    tempPlateFee: 0,
    deliveryEquipment: 0,

    // 重置赠品明细信息
    hasGiftItems: 'NO', // 默认为"无"
    giftItems: []
  })

  // 重置出库单位选择器
  selectedOutboundOrg.value = null
}

// 暴露方法给父组件
defineExpose({
  resetForm,
  setFormData: (data) => {
    const formData = { ...data }

    // 确保日期是时间戳格式
    if (formData.dealDate) {
      formData.dealDate = convertDateToTimestamp(formData.dealDate)
    }

    if (formData.expectedOutboundDate) {
      formData.expectedOutboundDate = convertDateToTimestamp(formData.expectedOutboundDate)
    }

    // 设置金额字段默认值
    if (formData.depositAmount === undefined) formData.depositAmount = 0
    if (formData.depositDeductible === undefined) formData.depositDeductible = true
    if (formData.totalSalePrice === undefined) formData.totalSalePrice = 0
    if (formData.discountAmount === undefined) formData.discountAmount = 0
    if (formData.exclusiveDiscountType === undefined) formData.exclusiveDiscountType = 'NONE'
    if (formData.exclusiveDiscountAmount === undefined) formData.exclusiveDiscountAmount = 0
    if (formData.salesExpense === undefined) formData.salesExpense = 0
    if (formData.finalPrice === undefined) formData.finalPrice = 0
    if (formData.finalPriceChinese === undefined) formData.finalPriceChinese = convertNumberToChinese(formData.finalPrice || 0)
    if (formData.totalSbPrice === undefined) formData.totalSbPrice = 0
    if (formData.profitRate === undefined) formData.profitRate = 0
    if (formData.profitAmount === undefined) formData.profitAmount = 0

    // 设置付款方式默认值
    if (formData.paymentMethod === undefined) formData.paymentMethod = 'FULL'

    // 设置贷款信息默认值
    if (formData.loanChannel === undefined) formData.loanChannel = ''
    if (formData.loanAmount === undefined) formData.loanAmount = 0
    if (formData.loanInitialAmount === undefined) formData.loanInitialAmount = 0
    if (formData.loanInitialRatio === undefined) formData.loanInitialRatio = 0
    if (formData.loanMonths === undefined) formData.loanMonths = null
    if (formData.loanRebateAmount === undefined) formData.loanRebateAmount = 0
    if (formData.loanRebateDeductible === undefined) formData.loanRebateDeductible = true
    if (formData.loanFee === undefined) formData.loanFee = 0

    // 设置二手车置换信息默认值
    if (formData.hasUsedVehicle === undefined) formData.hasUsedVehicle = 'NO'
    if (formData.usedVehicleId === undefined) formData.usedVehicleId = ''
    if (formData.usedVehicleVin === undefined) formData.usedVehicleVin = ''
    if (formData.usedVehicleAmount === undefined) formData.usedVehicleAmount = 0
    if (formData.usedVehicleDeductible === undefined) formData.usedVehicleDeductible = true
    if (formData.usedVehicleDiscountAmount === undefined) formData.usedVehicleDiscountAmount = 0
    if (formData.usedVehicleDiscountDeductible === undefined) formData.usedVehicleDiscountDeductible = true
    if (formData.usedVehicleBound === undefined) formData.usedVehicleBound = ''
    if (formData.usedVehicleModel === undefined) formData.usedVehicleModel = ''
    if (formData.usedVehicleColor === undefined) formData.usedVehicleColor = ''

    // 设置其他衍生收入信息默认值
    if (formData.hasDerivativeIncome === undefined) formData.hasDerivativeIncome = 'NO'
    if (formData.notaryFee === undefined) formData.notaryFee = 0
    if (formData.carefreeIncome === undefined) formData.carefreeIncome = 0
    if (formData.extendedWarrantyIncome === undefined) formData.extendedWarrantyIncome = 0
    if (formData.vpsIncome === undefined) formData.vpsIncome = 0
    if (formData.preInterest === undefined) formData.preInterest = 0
    if (formData.licensePlateFee === undefined) formData.licensePlateFee = 0
    if (formData.tempPlateFee === undefined) formData.tempPlateFee = 0
    if (formData.deliveryEquipment === undefined) formData.deliveryEquipment = 0

    // 设置赠品明细信息默认值
    if (formData.hasGiftItems === undefined) formData.hasGiftItems = 'NO'
    if (formData.giftItems === undefined) formData.giftItems = []

    Object.assign(form, formData)
  }
})
</script>

<style lang="scss">
@use './OrderEditModalNew.scss';
</style>
